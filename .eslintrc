{"root": true, "env": {"node": true, "es2022": true}, "rules": {"semi": "off", "quotes": "off", "indent": ["error", 4], "comma-dangle": "off", "arrow-parens": "off", "space-before-blocks": "off", "no-multi-spaces": "off", "prefer-const": "off", "no-trailing-spaces": "off", "no-multiple-empty-lines": "off", "array-bracket-spacing": "off", "spaced-comment": "off", "quote-props": "off", "no-else-return": "off", "eol-last": "off", "object-curly-spacing": "off", "no-unused-vars": "off", "space-infix-ops": "off", "space-before-function-paren": "off", "jsdoc/require-param": "off", "jsdoc/check-tag-names": "off", "jsdoc/check-param-names": "off", "no-restricted-modules": "off", "newline-per-chained-call": "off"}}