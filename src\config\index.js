import defaultConfig from './default.config';
import devConfig from './dev.config';
import produConfig from './produ.config';

const env = process.env.NODE_ENV || 'development';

/**
 * 配置信息
 */
let config = defaultConfig;

/**
 * 根据环境变量加载配置
 */
if (env === 'development') {
    config = { ...config, ...devConfig };
} else if (env === 'production') {
    config = { ...config, ...produConfig };
}

export default config;